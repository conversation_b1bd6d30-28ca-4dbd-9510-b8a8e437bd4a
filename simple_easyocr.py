#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jednoduchý EasyOCR skript pro odznaky
"""

import os
import re

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def test_easyocr():
    """
    Test EasyOCR instalace
    """
    try:
        print("🔍 Testuji EasyOCR...")
        import easyocr
        
        # Jednoduchý test bez GPU
        reader = easyocr.Reader(['en'], gpu=False, verbose=False)
        print("✅ EasyOCR se úspěšně inicializoval!")
        return reader
        
    except Exception as e:
        print(f"❌ Chyba při inicializaci EasyOCR: {e}")
        return None

def process_with_easyocr(image_path, reader):
    """
    Zpracuje obrázek s EasyOCR
    """
    try:
        print(f"🔍 Analyzuji: {os.path.basename(image_path)}")
        
        # Základní OCR
        results = reader.readtext(image_path, detail=0)
        
        if results:
            combined_text = ' '.join(results).strip()
            print(f"✅ Nalezen text: '{combined_text}'")
            return combined_text
        else:
            print("❌ Žádný text nenalezen")
            return None
            
    except Exception as e:
        print(f"❌ Chyba při OCR: {e}")
        return None

def main():
    """
    Hlavní funkce
    """
    print("🤖 Jednoduchý EasyOCR Test")
    print("=" * 50)
    
    # Test EasyOCR
    reader = test_easyocr()
    if not reader:
        print("❌ EasyOCR nefunguje, ukončuji...")
        return
    
    # Najdeme obrázky
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky!")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    # Zpracování
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}] {image_file}")
        
        text = process_with_easyocr(image_file, reader)
        
        if text:
            clean_text = clean_filename(text)
            if clean_text:
                _, ext = os.path.splitext(image_file)
                new_name = f"{clean_text}{ext}"
                
                # Kontrola duplicit
                if os.path.exists(new_name):
                    counter = 1
                    while os.path.exists(f"{clean_text}_{counter}{ext}"):
                        counter += 1
                    new_name = f"{clean_text}_{counter}{ext}"
                
                try:
                    os.rename(image_file, new_name)
                    print(f"✅ Přejmenováno na: {new_name}")
                except Exception as e:
                    print(f"❌ Chyba při přejmenování: {e}")
            else:
                print("❌ Nepodařilo se vyčistit text")
        else:
            print("❌ Žádný text nenalezen")

if __name__ == "__main__":
    main()
