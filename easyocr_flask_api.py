#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EasyOCR + Flask REST API pro odznaky
Lokální server bez registrace a API klíčů
"""

from flask import Flask, request, jsonify
import easyocr
import io
import numpy as np
from PIL import Image
import cv2

app = Flask(__name__)

# Globální proměnná pro EasyOCR reader
reader = None

def init_easyocr():
    """
    Inicializace EasyOCR readeru
    """
    global reader
    try:
        print("🔍 Inicializuji EasyOCR s češtinou a angličtinou...")
        reader = easyocr.Reader(['cs','en'], gpu=False)
        print("✅ EasyOCR úspěšně inicializován!")
        return True
    except Exception as e:
        print(f"❌ Chyba při inicializaci EasyOCR: {e}")
        return False

@app.route('/health', methods=['GET'])
def health():
    """
    Health check endpoint
    """
    return jsonify({
        'status': 'ok',
        'easyocr_ready': reader is not None,
        'message': 'EasyOCR Flask API je připraven'
    })

@app.route('/ocr', methods=['POST'])
def ocr():
    """
    OCR endpoint - zpracuje nahraný obrázek
    """
    try:
        if reader is None:
            return jsonify({
                'error': 'EasyOCR není inicializován',
                'text': ''
            }), 500
        
        if 'image' not in request.files:
            return jsonify({
                'error': 'Žádný obrázek nebyl nahrán',
                'text': ''
            }), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({
                'error': 'Prázdný název souboru',
                'text': ''
            }), 400
        
        # Načtení obrázku z uploadu
        img_bytes = file.read()
        
        # Převod na PIL Image
        img = Image.open(io.BytesIO(img_bytes))
        
        # Převod na numpy array pro EasyOCR
        img_array = np.array(img)
        
        print(f"🔍 Zpracovávám obrázek: {file.filename}")
        print(f"📏 Rozměry: {img_array.shape}")
        
        # OCR zpracování
        results = reader.readtext(img_array)
        
        # Extrakce textu
        texts = []
        details = []
        
        for detection in results:
            bbox, text, confidence = detection
            if confidence > 0.3:  # Pouze texty s dostatečnou spolehlivostí
                texts.append(text.strip())
                details.append({
                    'text': text.strip(),
                    'confidence': float(confidence),
                    'bbox': bbox
                })
        
        combined_text = ' '.join(texts)
        
        print(f"✅ Nalezeno {len(texts)} textů: '{combined_text}'")
        
        return jsonify({
            'text': combined_text,
            'details': details,
            'count': len(texts),
            'filename': file.filename
        })
        
    except Exception as e:
        print(f"❌ Chyba při OCR: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'error': str(e),
            'text': ''
        }), 500

@app.route('/ocr_file', methods=['POST'])
def ocr_file():
    """
    OCR endpoint pro lokální soubory (pro testování)
    """
    try:
        if reader is None:
            return jsonify({
                'error': 'EasyOCR není inicializován',
                'text': ''
            }), 500
        
        data = request.get_json()
        if not data or 'filepath' not in data:
            return jsonify({
                'error': 'Chybí filepath v JSON',
                'text': ''
            }), 400
        
        filepath = data['filepath']
        
        print(f"🔍 Zpracovávám soubor: {filepath}")
        
        # OCR zpracování přímo ze souboru
        results = reader.readtext(filepath)
        
        # Extrakce textu
        texts = []
        details = []
        
        for detection in results:
            bbox, text, confidence = detection
            if confidence > 0.3:
                texts.append(text.strip())
                details.append({
                    'text': text.strip(),
                    'confidence': float(confidence),
                    'bbox': bbox
                })
        
        combined_text = ' '.join(texts)
        
        print(f"✅ Nalezeno {len(texts)} textů: '{combined_text}'")
        
        return jsonify({
            'text': combined_text,
            'details': details,
            'count': len(texts),
            'filepath': filepath
        })
        
    except Exception as e:
        print(f"❌ Chyba při OCR: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'error': str(e),
            'text': ''
        }), 500

if __name__ == '__main__':
    print("🚀 EasyOCR + Flask REST API")
    print("=" * 50)
    print("🇨🇿 Lokální OCR server s češtinou a angličtinou")
    print("=" * 50)
    
    # Inicializace EasyOCR
    if init_easyocr():
        print("\n🌐 Spouštím Flask server...")
        print("📍 URL: http://localhost:5000")
        print("🔍 Health check: GET http://localhost:5000/health")
        print("📤 OCR upload: POST http://localhost:5000/ocr")
        print("📁 OCR file: POST http://localhost:5000/ocr_file")
        print("\n💡 Příklad použití:")
        print("curl -X POST http://localhost:5000/ocr -F image=@obrazek.jpg")
        print("\n🛑 Pro zastavení: Ctrl+C")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        print("❌ Nepodařilo se inicializovat EasyOCR - server se nespustí")
