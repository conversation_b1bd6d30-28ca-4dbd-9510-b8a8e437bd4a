#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleX v3.1 Advanced OCR pro odznaky
Používá nejnovější PP-OCRv5 s podporou českých diakritických znaků
"""

import os
import sys
import re

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def test_paddlex_v3_ocr():
    """
    Test nejnovějšího PaddleX v3.1 OCR s PP-OCRv5
    """
    try:
        print("🔍 Testuji PaddleX v3.1 s PP-OCRv5...")
        
        # Import PaddleX
        from paddlex import create_pipeline
        
        # Vytvoření OCR pipeline s PP-OCRv5
        pipeline = create_pipeline(pipeline="OCR")
        
        print("✅ PaddleX v3.1 OCR pipeline úspěšně vytvořen!")
        return pipeline
        
    except ImportError:
        print("❌ PaddleX v3.1 není nainstalován.")
        return None
    except Exception as e:
        print(f"❌ Chyba při inicializaci PaddleX v3.1 OCR: {e}")
        return None

def extract_text_paddlex_v3(image_path, pipeline):
    """
    Extrahuje text pomocí PaddleX v3.1 s pokročilými nastaveními
    """
    try:
        print(f"🔍 Analyzuji s PP-OCRv5: {os.path.basename(image_path)}")
        
        # Spuštění OCR s jednoduchým API
        result_generator = pipeline.predict(image_path)
        
        # Převedení generátoru na seznam
        results = list(result_generator)
        
        if not results:
            print("❌ Žádné výsledky z PP-OCRv5")
            return None
        
        result = results[0]
        
        # Extrakce textu z PP-OCRv5 výsledků
        texts = []
        
        if isinstance(result, dict) and 'rec_texts' in result and 'rec_scores' in result:
            rec_texts = result['rec_texts']
            rec_scores = result['rec_scores']
            
            print(f"🔍 PP-OCRv5 našlo {len(rec_texts)} textových segmentů")
            
            # Filtrování podle confidence a délky
            for i, text in enumerate(rec_texts):
                if text and text.strip():
                    confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                    text_clean = text.strip()
                    
                    # Přijmeme text s nižší confidence, ale delší než 2 znaky
                    if (confidence > 0.2 and len(text_clean) > 1) or (confidence > 0.5):
                        texts.append(text_clean)
                        print(f"  ✅ '{text_clean}' (conf: {confidence:.2f})")
                    else:
                        print(f"  ❌ '{text_clean}' (conf: {confidence:.2f}) - příliš nízká spolehlivost")
        
        if texts:
            # Inteligentní spojení textů
            combined_text = smart_combine_texts(texts)
            print(f"✅ Finální text: '{combined_text}'")
            return combined_text
        else:
            print("❌ Žádný text s dostatečnou spolehlivostí")
            return None
            
    except Exception as e:
        print(f"❌ Chyba při PP-OCRv5 OCR: {e}")
        return None

def smart_combine_texts(texts):
    """
    Inteligentně spojí texty podle jejich obsahu
    """
    if not texts:
        return ""
    
    if len(texts) == 1:
        return texts[0]
    
    # Pokusíme se identifikovat různé typy textů
    numbers = []
    words = []
    
    for text in texts:
        if text.isdigit():
            numbers.append(text)
        elif any(char.isalpha() for char in text):
            words.append(text)
        else:
            words.append(text)  # Smíšený obsah
    
    # Sestavíme finální text
    result_parts = []
    
    # Nejdříve čísla (často roky, čísla odznaků)
    if numbers:
        result_parts.extend(numbers)
    
    # Pak slova
    if words:
        result_parts.extend(words)
    
    return ' '.join(result_parts)

def process_image_v3(image_path, pipeline):
    """
    Zpracuje jeden obrázek pomocí PaddleX v3.1
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*60}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*60}")
    
    # Extrakce textu pomocí PaddleX v3.1
    extracted_text = extract_text_paddlex_v3(image_path, pipeline)
    
    if not extracted_text:
        print("❌ PP-OCRv5 nenašlo žádný text - přeskakuji")
        return False
    
    print(f"🎯 Rozpoznaný text: '{extracted_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(extracted_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí PaddleX v3.1
    """
    print("🚀 PaddleX v3.1 Advanced OCR Renamer pro odznaky")
    print("=" * 70)
    print("🎯 Používám nejnovější PP-OCRv5 s podporou českých diakritických znaků")
    print("=" * 70)
    
    # Test a inicializace PaddleX v3.1 OCR
    pipeline = test_paddlex_v3_ocr()
    if not pipeline:
        print("❌ Nepodařilo se inicializovat PaddleX v3.1 OCR")
        return
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s PaddleX v3.1...")
    print("⏱️  Používám nejnovější PP-OCRv5 pro maximální přesnost...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image_v3(image_file, pipeline)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n" + "="*70)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*70)

if __name__ == "__main__":
    main()
