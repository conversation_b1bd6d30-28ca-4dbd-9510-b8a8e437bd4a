#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vylepšený OCR skript pro čtení textu z odznaku na obrázku
"""

import os
import sys
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract
import re
import cv2
import numpy as np

def preprocess_image(image_path):
    """
    Předpracování obrázku pro lepší OCR
    """
    # Načtení obrázku pomocí OpenCV
    img = cv2.imread(image_path)

    # Konverze do odstínů šedi
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Zvětšení obrázku pro lepší rozpoznávání
    scale_factor = 3
    height, width = gray.shape
    gray = cv2.resize(gray, (width * scale_factor, height * scale_factor), interpolation=cv2.INTER_CUBIC)

    # Aplikace Gaussian blur pro odstran<PERSON>n<PERSON> šumu
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Adaptivní prahování pro lepší kontrast
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)

    # Morfologické operace pro vyčištění
    kernel = np.ones((2,2), np.uint8)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)

    # Uložení předpracovaného obrázku pro debug
    cv2.imwrite('preprocessed_debug.png', cleaned)

    return cleaned

def extract_text_from_image(image_path):
    """
    Extrahuje text z obrázku pomocí OCR s různými metodami
    """
    results = []

    try:
        # Metoda 1: Původní obrázek
        image = Image.open(image_path)

        # Různé konfigurace PSM (Page Segmentation Mode)
        psm_modes = [6, 7, 8, 13]

        for psm in psm_modes:
            config = f'--oem 3 --psm {psm}'
            text = pytesseract.image_to_string(image, lang='ces+eng', config=config)
            if text.strip():
                results.append(f"PSM {psm}: {text.strip()}")

        # Metoda 2: Předpracovaný obrázek
        preprocessed = preprocess_image(image_path)
        preprocessed_pil = Image.fromarray(preprocessed)

        for psm in psm_modes:
            config = f'--oem 3 --psm {psm}'
            text = pytesseract.image_to_string(preprocessed_pil, lang='ces+eng', config=config)
            if text.strip():
                results.append(f"Preprocessed PSM {psm}: {text.strip()}")

        # Metoda 3: Pouze velká písmena
        config_caps = '--oem 3 --psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ0123456789 '
        text_caps = pytesseract.image_to_string(preprocessed_pil, lang='ces+eng', config=config_caps)
        if text_caps.strip():
            results.append(f"Caps only: {text_caps.strip()}")

        return results

    except Exception as e:
        print(f"Chyba při čtení obrázku: {e}")
        return []

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    # Odstranění speciálních znaků a nahrazení mezerami
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def main():
    image_path = "VA_4_Dn_o.jpeg"  # Aktualizovaný název souboru

    if not os.path.exists(image_path):
        print(f"Soubor {image_path} neexistuje!")
        return

    print(f"Čtu text z obrázku: {image_path}")
    print("Zkouším různé metody OCR...")

    # Extrakce textu s různými metodami
    results = extract_text_from_image(image_path)

    if results:
        print("\n=== VÝSLEDKY OCR ===")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")

        print("\n=== VÝBĚR NEJLEPŠÍHO VÝSLEDKU ===")
        print("Který výsledek vypadá nejlépe? (zadejte číslo 1-{})".format(len(results)))
        print("Nebo zadejte 'm' pro manuální zadání textu")
        print("Nebo zadejte 'q' pro ukončení bez přejmenování")

        try:
            choice = input("Vaše volba: ").strip()

            if choice.lower() == 'q':
                print("Ukončuji bez přejmenování")
                return

            choice_num = int(choice)
            if 1 <= choice_num <= len(results):
                selected_result = results[choice_num - 1]
                # Extrakce pouze textu (bez prefixu s metodou)
                text_part = selected_result.split(': ', 1)[1] if ': ' in selected_result else selected_result

                print(f"Vybrali jste: '{text_part}'")

                # Vyčištění textu pro název souboru
                clean_text = clean_filename(text_part)

                if clean_text:
                    # Získání přípony původního souboru
                    _, ext = os.path.splitext(image_path)
                    new_filename = f"{clean_text}{ext}"

                    print(f"Navrhovaný nový název: {new_filename}")

                    # Kontrola, jestli soubor s tímto názvem už neexistuje
                    if os.path.exists(new_filename):
                        counter = 1
                        while os.path.exists(f"{clean_text}_{counter}{ext}"):
                            counter += 1
                        new_filename = f"{clean_text}_{counter}{ext}"
                        print(f"Soubor už existuje, použiji: {new_filename}")

                    # Přejmenování souboru
                    try:
                        os.rename(image_path, new_filename)
                        print(f"Soubor úspěšně přejmenován na: {new_filename}")
                    except Exception as e:
                        print(f"Chyba při přejmenování: {e}")
                else:
                    print("Nepodařilo se vyčistit text pro název souboru")
            else:
                print("Neplatná volba!")

        except ValueError:
            print("Neplatná volba!")
        except KeyboardInterrupt:
            print("\nUkončuji...")
    else:
        print("Nepodařilo se přečíst žádný text z obrázku")

    # Vyčištění dočasných souborů
    if os.path.exists('preprocessed_debug.png'):
        os.remove('preprocessed_debug.png')

if __name__ == "__main__":
    main()
