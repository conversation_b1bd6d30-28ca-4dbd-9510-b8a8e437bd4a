#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR skript pro čtení textu z odznaku na obrázku
"""

import os
import sys
from PIL import Image
import pytesseract
import re

def extract_text_from_image(image_path):
    """
    Extrahuje text z obrázku pomocí OCR
    """
    try:
        # Otevření obrázku
        image = Image.open(image_path)
        
        # Konfigurace Tesseract pro lepší rozpoznávání
        # --psm 6: Uniform block of text
        # --oem 3: Default OCR Engine Mode
        custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ '
        
        # Extrakce textu
        text = pytesseract.image_to_string(image, lang='ces+eng', config=custom_config)
        
        return text.strip()
        
    except Exception as e:
        print(f"Chyba při čtení obrázku: {e}")
        return None

def clean_filename(text):
    """
    Vyčistí text pro použití jako n<PERSON>zev souboru
    """
    # Odstranění speciálních znaků a nahrazení mezerami
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def main():
    image_path = "33100297.jpeg"
    
    if not os.path.exists(image_path):
        print(f"Soubor {image_path} neexistuje!")
        return
    
    print(f"Čtu text z obrázku: {image_path}")
    
    # Extrakce textu
    extracted_text = extract_text_from_image(image_path)
    
    if extracted_text:
        print(f"Nalezený text: '{extracted_text}'")
        
        # Vyčištění textu pro název souboru
        clean_text = clean_filename(extracted_text)
        
        if clean_text:
            # Získání přípony původního souboru
            _, ext = os.path.splitext(image_path)
            new_filename = f"{clean_text}{ext}"
            
            print(f"Navrhovaný nový název: {new_filename}")
            
            # Kontrola, jestli soubor s tímto názvem už neexistuje
            if os.path.exists(new_filename):
                counter = 1
                while os.path.exists(f"{clean_text}_{counter}{ext}"):
                    counter += 1
                new_filename = f"{clean_text}_{counter}{ext}"
                print(f"Soubor už existuje, použiji: {new_filename}")
            
            # Přejmenování souboru
            try:
                os.rename(image_path, new_filename)
                print(f"Soubor úspěšně přejmenován na: {new_filename}")
            except Exception as e:
                print(f"Chyba při přejmenování: {e}")
        else:
            print("Nepodařilo se vyčistit text pro název souboru")
    else:
        print("Nepodařilo se přečíst žádný text z obrázku")

if __name__ == "__main__":
    main()
