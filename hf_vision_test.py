#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test skript pro analýzu obrázku pomocí Hugging Face Spaces
"""

import os
import requests
import json
import time
import re
from PIL import Image

def test_with_llava_space(image_path):
    """
    Testuje s LLaVA modelem přes Hugging Face Spaces
    """
    try:
        print("Zkouším LLaVA model...")
        
        # Hugging Face Spaces API endpoint pro LLaVA
        api_url = "https://badayvedat-llava.hf.space/api/predict"
        
        # Otevření obrázku
        with open(image_path, 'rb') as f:
            files = {'file': f}
            
            # Parametry pro LLaVA
            data = {
                'data': [
                    None,  # image placeholder
                    "What text is written on this badge? Please provide only the exact text you can see.",  # prompt
                    0.7,   # temperature
                    512,   # max_tokens
                    0.9    # top_p
                ]
            }
            
            response = requests.post(api_url, files=files, data={'data': json.dumps(data['data'])}, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and len(result['data']) > 0:
                    return result['data'][0]
            
        return None
        
    except Exception as e:
        print(f"Chyba s LLaVA: {e}")
        return None

def test_with_blip2_space(image_path):
    """
    Testuje s BLIP-2 modelem přes Hugging Face Spaces
    """
    try:
        print("Zkouším BLIP-2 model...")
        
        # BLIP-2 endpoint
        api_url = "https://salesforce-blip2.hf.space/api/predict"
        
        with open(image_path, 'rb') as f:
            files = {'file': f}
            
            data = {
                'data': [
                    None,  # image placeholder
                    "What text is written on this badge?",  # question
                    "Question Answering"  # task type
                ]
            }
            
            response = requests.post(api_url, files=files, data={'data': json.dumps(data['data'])}, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and len(result['data']) > 0:
                    return result['data'][0]
            
        return None
        
    except Exception as e:
        print(f"Chyba s BLIP-2: {e}")
        return None

def test_with_gradio_client(image_path):
    """
    Alternativní přístup pomocí gradio_client
    """
    try:
        # Zkusíme nainstalovat gradio_client pokud není
        import gradio_client
        
        print("Zkouším přes Gradio Client...")
        
        # LLaVA space
        client = gradio_client.Client("https://badayvedat-llava.hf.space/")
        
        result = client.predict(
            image_path,  # image
            "What text is written on this badge? Please provide only the exact text you can see.",  # prompt
            0.7,  # temperature
            512,  # max_tokens
            0.9,  # top_p
            api_name="/predict"
        )
        
        return result
        
    except ImportError:
        print("gradio_client není nainstalován. Zkouším nainstalovat...")
        try:
            import subprocess
            subprocess.check_call(["pip", "install", "gradio_client"])
            import gradio_client
            
            client = gradio_client.Client("https://badayvedat-llava.hf.space/")
            result = client.predict(
                image_path,
                "What text is written on this badge? Please provide only the exact text you can see.",
                0.7, 512, 0.9,
                api_name="/predict"
            )
            return result
        except Exception as e:
            print(f"Nepodařilo se nainstalovat gradio_client: {e}")
            return None
    except Exception as e:
        print(f"Chyba s Gradio Client: {e}")
        return None

def test_simple_ocr_api(image_path):
    """
    Zkusí jednoduchý OCR API
    """
    try:
        print("Zkouším OCR.space API...")
        
        # OCR.space má bezplatný tier
        api_url = "https://api.ocr.space/parse/image"
        
        with open(image_path, 'rb') as f:
            files = {'file': f}
            data = {
                'apikey': 'helloworld',  # bezplatný demo klíč
                'language': 'eng',
                'isOverlayRequired': False,
                'detectOrientation': True,
                'scale': True,
                'OCREngine': 2
            }
            
            response = requests.post(api_url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'ParsedResults' in result and len(result['ParsedResults']) > 0:
                    text = result['ParsedResults'][0].get('ParsedText', '').strip()
                    if text:
                        return text
        
        return None
        
    except Exception as e:
        print(f"Chyba s OCR.space: {e}")
        return None

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def main():
    image_path = "q.jpeg"
    
    if not os.path.exists(image_path):
        print(f"Soubor {image_path} neexistuje!")
        return
    
    print(f"Testuji různé AI vision služby s obrázkem: {image_path}")
    print("=" * 50)
    
    results = []
    
    # Test 1: OCR.space (nejjednodušší)
    ocr_result = test_simple_ocr_api(image_path)
    if ocr_result:
        results.append(f"OCR.space: {ocr_result}")
        print(f"✓ OCR.space: {ocr_result}")
    else:
        print("✗ OCR.space nedostupné")
    
    # Test 2: Gradio Client
    gradio_result = test_with_gradio_client(image_path)
    if gradio_result:
        results.append(f"LLaVA (Gradio): {gradio_result}")
        print(f"✓ LLaVA (Gradio): {gradio_result}")
    else:
        print("✗ LLaVA (Gradio) nedostupné")
    
    # Test 3: Přímé API volání
    llava_result = test_with_llava_space(image_path)
    if llava_result:
        results.append(f"LLaVA (API): {llava_result}")
        print(f"✓ LLaVA (API): {llava_result}")
    else:
        print("✗ LLaVA (API) nedostupné")
    
    # Test 4: BLIP-2
    blip_result = test_with_blip2_space(image_path)
    if blip_result:
        results.append(f"BLIP-2: {blip_result}")
        print(f"✓ BLIP-2: {blip_result}")
    else:
        print("✗ BLIP-2 nedostupné")
    
    print("\n" + "=" * 50)
    
    if results:
        print(f"NALEZENÉ VÝSLEDKY:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")
        
        print(f"\nKterý výsledek vypadá nejlépe? (1-{len(results)})")
        print("Nebo 'm' pro manuální zadání, 'q' pro ukončení")
        
        choice = input("Vaše volba: ").strip()
        
        if choice.lower() == 'q':
            print("Ukončuji...")
            return
        elif choice.lower() == 'm':
            manual_text = input("Zadejte správný text z odznaku: ").strip()
            if manual_text:
                selected_text = manual_text
            else:
                print("Prázdný text, ukončuji")
                return
        else:
            try:
                choice_num = int(choice)
                if 1 <= choice_num <= len(results):
                    selected_result = results[choice_num - 1]
                    selected_text = selected_result.split(': ', 1)[1] if ': ' in selected_result else selected_result
                else:
                    print("Neplatná volba!")
                    return
            except ValueError:
                print("Neplatná volba!")
                return
        
        # Přejmenování souboru
        clean_text = clean_filename(selected_text)
        if clean_text:
            _, ext = os.path.splitext(image_path)
            new_filename = f"{clean_text}{ext}"
            
            if os.path.exists(new_filename):
                counter = 1
                while os.path.exists(f"{clean_text}_{counter}{ext}"):
                    counter += 1
                new_filename = f"{clean_text}_{counter}{ext}"
            
            try:
                os.rename(image_path, new_filename)
                print(f"\n✅ Soubor úspěšně přejmenován na: {new_filename}")
            except Exception as e:
                print(f"❌ Chyba při přejmenování: {e}")
        else:
            print("❌ Nepodařilo se vyčistit text pro název souboru")
    else:
        print("❌ Žádná služba nevrátila výsledek.")
        print("Zkuste zadat text manuálně:")
        manual_text = input("Text z odznaku: ").strip()
        if manual_text:
            clean_text = clean_filename(manual_text)
            if clean_text:
                _, ext = os.path.splitext(image_path)
                new_filename = f"{clean_text}{ext}"
                try:
                    os.rename(image_path, new_filename)
                    print(f"✅ Soubor přejmenován na: {new_filename}")
                except Exception as e:
                    print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    main()
