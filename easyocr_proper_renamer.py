#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EasyOCR správný renamer pro odznaky
Podle instrukcí s českým a anglickým jazykem
"""

import os
import sys
import re
import easyocr
from PIL import Image, ImageDraw

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def test_easyocr():
    """
    Test EasyOCR s češtinou a angličtinou
    """
    try:
        print("🔍 Inicializuji EasyOCR s češtinou a angličtinou...")
        # Načtení OCR čtečky – čeština a angličtina, CPU režim
        reader = easyocr.Reader(['cs','en'], gpu=False)
        print("✅ EasyOCR úspěšně inicializován!")
        return reader
        
    except Exception as e:
        print(f"❌ Chyba při inicializaci EasyOCR: {e}")
        return None

def extract_text_easyocr(image_path, reader):
    """
    Extrahuje text pomocí EasyOCR s různými nastaveními
    """
    try:
        print(f"🔍 Analyzuji: {os.path.basename(image_path)}")
        
        results = []
        
        # Metoda 1: Základní OCR s detaily
        try:
            result1 = reader.readtext(image_path, detail=1)
            if result1:
                texts = []
                for box, text, conf in result1:
                    if conf > 0.3:  # Pouze texty s dostatečnou spolehlivostí
                        texts.append(f"{text.strip()} (conf: {conf:.2f})")
                        print(f"  ✅ '{text.strip()}' (confidence: {conf:.2f})")
                    else:
                        print(f"  ❌ '{text.strip()}' (confidence: {conf:.2f}) - nízká spolehlivost")
                
                if texts:
                    results.append(f"Detailní: {' | '.join(texts)}")
        except Exception as e:
            print(f"   ⚠️ Metoda 1 selhala: {e}")
        
        # Metoda 2: Jednoduchý výstup pouze text
        try:
            texts2 = reader.readtext(image_path, detail=0)
            if texts2:
                combined_text = ' '.join([t.strip() for t in texts2 if t.strip()])
                if combined_text:
                    results.append(f"Jednoduchý: {combined_text}")
        except Exception as e:
            print(f"   ⚠️ Metoda 2 selhala: {e}")
        
        # Metoda 3: Odstavce
        try:
            texts3 = reader.readtext(image_path, detail=0, paragraph=True)
            if texts3:
                combined_text = ' '.join([t.strip() for t in texts3 if t.strip()])
                if combined_text:
                    results.append(f"Odstavce: {combined_text}")
        except Exception as e:
            print(f"   ⚠️ Metoda 3 selhala: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Chyba při EasyOCR: {e}")
        return []

def visualize_results(image_path, reader):
    """
    Vizualizuje výsledky OCR na obrázku
    """
    try:
        result = reader.readtext(image_path, detail=1)
        
        if not result:
            return
        
        img = Image.open(image_path)
        draw = ImageDraw.Draw(img)
        
        for box, text, conf in result:
            if conf > 0.3:
                # Kreslení rámečku kolem textu
                draw.rectangle([tuple(box[0]), tuple(box[2])], outline='green', width=2)
                # Přidání textu
                draw.text(tuple(box[0]), f"{text} ({conf:.2f})", fill='red')
        
        # Uložení vizualizace
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        viz_path = f"{base_name}_ocr_visualization.png"
        img.save(viz_path)
        print(f"📊 Vizualizace uložena: {viz_path}")
        
    except Exception as e:
        print(f"❌ Chyba při vizualizaci: {e}")

def process_image(image_path, reader):
    """
    Zpracuje jeden obrázek pomocí EasyOCR
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*60}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*60}")
    
    # Extrakce textu pomocí EasyOCR
    results = extract_text_easyocr(image_path, reader)
    
    if not results:
        print("❌ EasyOCR nenašlo žádný text - přeskakuji")
        return False
    
    print(f"📋 Nalezené texty:")
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result}")
    
    # Automaticky vybereme první (nejlepší) výsledek
    selected_result = results[0]
    
    # Extrakce čistého textu
    if ': ' in selected_result:
        selected_text = selected_result.split(': ', 1)[1]
        # Odstranění confidence informací
        if ' | ' in selected_text:
            # Pro detailní výsledky vezmeme pouze texty bez confidence
            parts = selected_text.split(' | ')
            clean_parts = []
            for part in parts:
                if ' (conf:' in part:
                    clean_parts.append(part.split(' (conf:')[0])
                else:
                    clean_parts.append(part)
            selected_text = ' '.join(clean_parts)
    else:
        selected_text = selected_result
    
    print(f"🎯 Vybrán text: '{selected_text}'")
    
    # Vytvoření vizualizace
    visualize_results(image_path, reader)
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí EasyOCR
    """
    print("🤖 EasyOCR Správný Renamer pro odznaky")
    print("=" * 60)
    print("🇨🇿 Používám EasyOCR s češtinou a angličtinou (CPU režim)")
    print("=" * 60)
    
    # Test a inicializace EasyOCR
    reader = test_easyocr()
    if not reader:
        print("❌ Nepodařilo se inicializovat EasyOCR")
        return
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram') and not file.endswith('_ocr_visualization.png'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s EasyOCR...")
    print("⏱️  První spuštění stáhne jazykové modely...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file, reader)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
