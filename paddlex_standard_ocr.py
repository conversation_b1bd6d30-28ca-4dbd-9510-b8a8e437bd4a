#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleX Standard OCR pro odznaky
Používá PaddleX bez high-performance inference
"""

import os
import sys
import re

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezen<PERSON> délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def test_paddlex_ocr():
    """
    Test PaddleX OCR bez high-performance inference
    """
    try:
        print("🔍 Testuji PaddleX OCR...")
        
        # Import PaddleX
        from paddlex import create_pipeline
        
        # Vytvoření OCR pipeline bez high-performance inference
        pipeline = create_pipeline(pipeline="OCR")
        
        print("✅ PaddleX OCR pipeline úspěšně vytvořen!")
        return pipeline
        
    except ImportError:
        print("❌ PaddleX není nainstalován. Instaluji...")
        try:
            os.system("pip install paddlex")
            return test_paddlex_ocr()
        except:
            return None
    except Exception as e:
        print(f"❌ Chyba při inicializaci PaddleX OCR: {e}")
        return None

def extract_text_paddlex(image_path, pipeline):
    """
    Extrahuje text pomocí PaddleX OCR
    """
    try:
        print(f"🔍 Analyzuji: {os.path.basename(image_path)}")
        
        # Spuštění OCR
        result_generator = pipeline.predict(image_path)

        # PaddleX vrací generator, musíme ho převést na seznam
        results = list(result_generator)

        print(f"🔍 Debug - počet výsledků: {len(results)}")

        if not results:
            print("❌ Žádné výsledky z generátoru")
            return None

        # Vezmeme první výsledek
        result = results[0]

        print(f"🔍 Debug - typ výsledku: {type(result)}")
        print(f"🔍 Debug - klíče výsledku: {result.keys() if isinstance(result, dict) else 'není dict'}")

        # Pokusíme se najít text v různých možných strukturách
        texts = []

        if isinstance(result, dict):
            # PaddleX OCR výsledek má rec_texts a rec_scores
            if 'rec_texts' in result and 'rec_scores' in result:
                rec_texts = result['rec_texts']
                rec_scores = result['rec_scores']

                for i, text in enumerate(rec_texts):
                    if text and text.strip():
                        confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                        if confidence > 0.3:  # Pouze texty s dostatečnou spolehlivostí
                            texts.append(text.strip())

            # Fallback možnosti
            elif 'text_recognition' in result:
                for item in result['text_recognition']:
                    if isinstance(item, dict) and 'text' in item:
                        text = item['text'].strip()
                        confidence = item.get('score', 0.0)
                        if text and confidence > 0.3:
                            texts.append(text)

            elif 'text' in result:
                if result['text']:
                    texts.append(result['text'])
        
        if texts:
            # Spojíme všechny texty
            combined_text = ' '.join(texts).strip()
            print(f"✅ Nalezen text: '{combined_text}'")
            return combined_text
        else:
            print("❌ Žádný text nenalezen")
            print(f"🔍 Debug - celý výsledek: {result}")
            return None
            
    except Exception as e:
        print(f"❌ Chyba při PaddleX OCR: {e}")
        return None

def process_image(image_path, pipeline):
    """
    Zpracuje jeden obrázek pomocí PaddleX OCR
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")
    
    # Extrakce textu pomocí PaddleX
    extracted_text = extract_text_paddlex(image_path, pipeline)
    
    if not extracted_text:
        print("❌ PaddleX nenašlo žádný text - přeskakuji")
        return False
    
    print(f"🎯 Rozpoznaný text: '{extracted_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(extracted_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí PaddleX OCR
    """
    print("🚀 PaddleX Standard OCR Renamer pro odznaky")
    print("=" * 60)
    print("🎯 Používám PaddleX standardní OCR pipeline")
    print("=" * 60)
    
    # Test a inicializace PaddleX OCR
    pipeline = test_paddlex_ocr()
    if not pipeline:
        print("❌ Nepodařilo se inicializovat PaddleX OCR")
        return
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s PaddleX OCR...")
    print("⏱️  První spuštění může trvat déle kvůli stahování modelů...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file, pipeline)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
