#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test PyTorch kompatibility
"""

print("🔍 Testuji PyTorch kompatibilitu...")

try:
    import torch
    print(f"✅ PyTorch importován: verze {torch.__version__}")
    
    # Test základních operací
    print("🔍 Testuji základní tensor operace...")
    x = torch.randn(2, 3)
    print(f"✅ Tensor vytvořen: {x.shape}")
    
    # Test CPU operací
    print("🔍 Testuji CPU operace...")
    y = x + 1
    print(f"✅ CPU operace úspěšná: {y.shape}")
    
    print("🎉 PyTorch funguje správně!")
    
except Exception as e:
    print(f"❌ Chyba v PyTorch: {e}")
    import traceback
    traceback.print_exc()
