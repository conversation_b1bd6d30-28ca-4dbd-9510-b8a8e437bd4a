#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleX + Flask REST API pro odznaky
Lokální server s PaddleX PP-OCRv5 místo EasyOCR
"""

from flask import Flask, request, jsonify
import io
import numpy as np
from PIL import Image
import os

app = Flask(__name__)

# Globální proměnná pro PaddleX pipeline
pipeline = None

def init_paddlex():
    """
    Inicializace PaddleX OCR pipeline
    """
    global pipeline
    try:
        print("🔍 Inicializuji PaddleX s PP-OCRv5...")
        from paddlex import create_pipeline
        pipeline = create_pipeline(pipeline="OCR")
        print("✅ PaddleX úspěšně inicializován!")
        return True
    except Exception as e:
        print(f"❌ Chyba při inicializaci PaddleX: {e}")
        return False

@app.route('/health', methods=['GET'])
def health():
    """
    Health check endpoint
    """
    return jsonify({
        'status': 'ok',
        'paddlex_ready': pipeline is not None,
        'message': 'PaddleX Flask API je připraven',
        'engine': 'PaddleX PP-OCRv5'
    })

@app.route('/ocr', methods=['POST'])
def ocr():
    """
    OCR endpoint - zpracuje nahraný obrázek
    """
    try:
        if pipeline is None:
            return jsonify({
                'error': 'PaddleX není inicializován',
                'text': ''
            }), 500
        
        if 'image' not in request.files:
            return jsonify({
                'error': 'Žádný obrázek nebyl nahrán',
                'text': ''
            }), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({
                'error': 'Prázdný název souboru',
                'text': ''
            }), 400
        
        # Uložení dočasného souboru pro PaddleX
        temp_filename = f"temp_{file.filename}"
        file.save(temp_filename)
        
        try:
            print(f"🔍 Zpracovávám obrázek: {file.filename}")
            
            # OCR zpracování pomocí PaddleX
            result_generator = pipeline.predict(temp_filename)
            results = list(result_generator)
            
            if not results:
                return jsonify({
                    'text': '',
                    'details': [],
                    'count': 0,
                    'filename': file.filename,
                    'engine': 'PaddleX PP-OCRv5'
                })
            
            result = results[0]
            
            # Extrakce textu z PaddleX výsledků
            texts = []
            details = []
            
            if isinstance(result, dict) and 'rec_texts' in result and 'rec_scores' in result:
                rec_texts = result['rec_texts']
                rec_scores = result['rec_scores']
                
                for i, text in enumerate(rec_texts):
                    if text and text.strip():
                        confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                        if confidence > 0.2:  # Nižší práh pro API
                            text_clean = text.strip()
                            texts.append(text_clean)
                            details.append({
                                'text': text_clean,
                                'confidence': float(confidence),
                                'index': i
                            })
            
            combined_text = ' '.join(texts)
            
            print(f"✅ Nalezeno {len(texts)} textů: '{combined_text}'")
            
            return jsonify({
                'text': combined_text,
                'details': details,
                'count': len(texts),
                'filename': file.filename,
                'engine': 'PaddleX PP-OCRv5'
            })
            
        finally:
            # Smazání dočasného souboru
            if os.path.exists(temp_filename):
                os.remove(temp_filename)
        
    except Exception as e:
        print(f"❌ Chyba při OCR: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'error': str(e),
            'text': '',
            'engine': 'PaddleX PP-OCRv5'
        }), 500

@app.route('/ocr_file', methods=['POST'])
def ocr_file():
    """
    OCR endpoint pro lokální soubory
    """
    try:
        if pipeline is None:
            return jsonify({
                'error': 'PaddleX není inicializován',
                'text': ''
            }), 500
        
        data = request.get_json()
        if not data or 'filepath' not in data:
            return jsonify({
                'error': 'Chybí filepath v JSON',
                'text': ''
            }), 400
        
        filepath = data['filepath']
        
        if not os.path.exists(filepath):
            return jsonify({
                'error': f'Soubor {filepath} neexistuje',
                'text': ''
            }), 404
        
        print(f"🔍 Zpracovávám soubor: {filepath}")
        
        # OCR zpracování pomocí PaddleX
        result_generator = pipeline.predict(filepath)
        results = list(result_generator)
        
        if not results:
            return jsonify({
                'text': '',
                'details': [],
                'count': 0,
                'filepath': filepath,
                'engine': 'PaddleX PP-OCRv5'
            })
        
        result = results[0]
        
        # Extrakce textu
        texts = []
        details = []
        
        if isinstance(result, dict) and 'rec_texts' in result and 'rec_scores' in result:
            rec_texts = result['rec_texts']
            rec_scores = result['rec_scores']
            
            for i, text in enumerate(rec_texts):
                if text and text.strip():
                    confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                    if confidence > 0.2:
                        text_clean = text.strip()
                        texts.append(text_clean)
                        details.append({
                            'text': text_clean,
                            'confidence': float(confidence),
                            'index': i
                        })
        
        combined_text = ' '.join(texts)
        
        print(f"✅ Nalezeno {len(texts)} textů: '{combined_text}'")
        
        return jsonify({
            'text': combined_text,
            'details': details,
            'count': len(texts),
            'filepath': filepath,
            'engine': 'PaddleX PP-OCRv5'
        })
        
    except Exception as e:
        print(f"❌ Chyba při OCR: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'error': str(e),
            'text': '',
            'engine': 'PaddleX PP-OCRv5'
        }), 500

@app.route('/rename', methods=['POST'])
def rename_files():
    """
    Speciální endpoint pro přejmenování souborů podle OCR
    """
    try:
        if pipeline is None:
            return jsonify({
                'error': 'PaddleX není inicializován',
                'results': []
            }), 500
        
        data = request.get_json()
        directory = data.get('directory', '.')
        
        # Najdeme všechny obrázky
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        image_files = []
        
        for file in os.listdir(directory):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                if not file.startswith('Příbram'):  # Přeskočíme už přejmenované
                    image_files.append(file)
        
        results = []
        
        for image_file in image_files:
            filepath = os.path.join(directory, image_file)
            
            try:
                # OCR zpracování
                result_generator = pipeline.predict(filepath)
                ocr_results = list(result_generator)
                
                if ocr_results:
                    result = ocr_results[0]
                    
                    if isinstance(result, dict) and 'rec_texts' in result and 'rec_scores' in result:
                        rec_texts = result['rec_texts']
                        rec_scores = result['rec_scores']
                        
                        texts = []
                        for i, text in enumerate(rec_texts):
                            if text and text.strip():
                                confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                                if confidence > 0.2:
                                    texts.append(text.strip())
                        
                        if texts:
                            combined_text = ' '.join(texts)
                            
                            # Vyčištění pro název souboru
                            import re
                            clean_text = re.sub(r'[<>:"/\\|?*]', '', combined_text)
                            clean_text = re.sub(r'\s+', '_', clean_text)
                            clean_text = clean_text.strip('_')
                            
                            if len(clean_text) > 50:
                                clean_text = clean_text[:50]
                            
                            if clean_text:
                                _, ext = os.path.splitext(image_file)
                                new_filename = f"{clean_text}{ext}"
                                
                                # Kontrola duplicit
                                counter = 1
                                while os.path.exists(os.path.join(directory, new_filename)):
                                    new_filename = f"{clean_text}_{counter}{ext}"
                                    counter += 1
                                
                                # Přejmenování
                                old_path = os.path.join(directory, image_file)
                                new_path = os.path.join(directory, new_filename)
                                os.rename(old_path, new_path)
                                
                                results.append({
                                    'old_name': image_file,
                                    'new_name': new_filename,
                                    'text': combined_text,
                                    'status': 'success'
                                })
                            else:
                                results.append({
                                    'old_name': image_file,
                                    'new_name': image_file,
                                    'text': combined_text,
                                    'status': 'failed',
                                    'reason': 'Nepodařilo se vyčistit text'
                                })
                        else:
                            results.append({
                                'old_name': image_file,
                                'new_name': image_file,
                                'text': '',
                                'status': 'failed',
                                'reason': 'Žádný text nenalezen'
                            })
                    else:
                        results.append({
                            'old_name': image_file,
                            'new_name': image_file,
                            'text': '',
                            'status': 'failed',
                            'reason': 'Neočekávaný formát výsledku'
                        })
                else:
                    results.append({
                        'old_name': image_file,
                        'new_name': image_file,
                        'text': '',
                        'status': 'failed',
                        'reason': 'Žádné OCR výsledky'
                    })
                    
            except Exception as e:
                results.append({
                    'old_name': image_file,
                    'new_name': image_file,
                    'text': '',
                    'status': 'error',
                    'reason': str(e)
                })
        
        successful = len([r for r in results if r['status'] == 'success'])
        
        return jsonify({
            'results': results,
            'total': len(results),
            'successful': successful,
            'failed': len(results) - successful,
            'engine': 'PaddleX PP-OCRv5'
        })
        
    except Exception as e:
        print(f"❌ Chyba při hromadném přejmenování: {e}")
        import traceback
        traceback.print_exc()
        
        return jsonify({
            'error': str(e),
            'results': [],
            'engine': 'PaddleX PP-OCRv5'
        }), 500

if __name__ == '__main__':
    print("🚀 PaddleX + Flask REST API")
    print("=" * 50)
    print("🇨🇿 Lokální OCR server s PaddleX PP-OCRv5")
    print("=" * 50)
    
    # Inicializace PaddleX
    if init_paddlex():
        print("\n🌐 Spouštím Flask server...")
        print("📍 URL: http://localhost:5000")
        print("🔍 Health check: GET http://localhost:5000/health")
        print("📤 OCR upload: POST http://localhost:5000/ocr")
        print("📁 OCR file: POST http://localhost:5000/ocr_file")
        print("🔄 Hromadné přejmenování: POST http://localhost:5000/rename")
        print("\n💡 Příklady použití:")
        print("curl -X POST http://localhost:5000/ocr -F image=@obrazek.jpg")
        print('curl -X POST http://localhost:5000/ocr_file -H "Content-Type: application/json" -d \'{"filepath": "obrazek.jpg"}\'')
        print('curl -X POST http://localhost:5000/rename -H "Content-Type: application/json" -d \'{"directory": "."}\'')
        print("\n🛑 Pro zastavení: Ctrl+C")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        print("❌ Nepodařilo se inicializovat PaddleX - server se nespustí")
