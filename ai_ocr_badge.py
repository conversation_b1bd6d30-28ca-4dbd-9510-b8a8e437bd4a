#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI OCR skript pro čtení textu z odznaku pomocí bezplatné AI služby
"""

import os
import sys
import base64
import requests
import json
import re
from PIL import Image

def encode_image_to_base64(image_path):
    """
    Převede obrázek na base64 string
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def analyze_with_ollama(image_path):
    """
    Analyzuje obrázek pomocí Ollama s vision modelem
    """
    try:
        # Zkusíme nejdříve llava model
        base64_image = encode_image_to_base64(image_path)
        
        url = "http://localhost:11434/api/generate"
        
        payload = {
            "model": "llava",
            "prompt": "What text is written on the badge in this image? Please provide only the exact text you can see, nothing else.",
            "images": [base64_image],
            "stream": False
        }
        
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result.get('response', '').strip()
        else:
            print(f"Ollama chyba: {response.status_code}")
            return None
            
    except requests.exceptions.ConnectionError:
        print("Ollama není spuštěná nebo není dostupná na localhost:11434")
        return None
    except Exception as e:
        print(f"Chyba při komunikaci s Ollama: {e}")
        return None

def analyze_with_huggingface(image_path):
    """
    Analyzuje obrázek pomocí Hugging Face Inference API (bezplatné)
    """
    try:
        # Použijeme Microsoft's TrOCR model
        API_URL = "https://api-inference.huggingface.co/models/microsoft/trocr-base-printed"
        
        with open(image_path, "rb") as f:
            data = f.read()
        
        response = requests.post(API_URL, data=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                return result[0].get('generated_text', '').strip()
            elif isinstance(result, dict):
                return result.get('generated_text', '').strip()
        else:
            print(f"Hugging Face chyba: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"Chyba při komunikaci s Hugging Face: {e}")
        return None

def analyze_with_google_vision_free(image_path):
    """
    Pokus o použití Google Vision API přes bezplatný endpoint
    """
    try:
        # Toto je experimentální - Google má omezené bezplatné API
        base64_image = encode_image_to_base64(image_path)
        
        # Alternativně můžeme zkusit jiné bezplatné OCR API
        print("Google Vision API vyžaduje API klíč...")
        return None
        
    except Exception as e:
        print(f"Chyba s Google Vision: {e}")
        return None

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků a nahrazení mezerami
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def main():
    image_path = "Ee_VA_4_Dn_==.jpeg"  # Aktuální název souboru
    
    if not os.path.exists(image_path):
        print(f"Soubor {image_path} neexistuje!")
        return
    
    print(f"Analyzuji obrázek pomocí AI: {image_path}")
    print("Zkouším různé AI služby...")
    
    results = []
    
    # Zkusíme Ollama
    print("\n1. Zkouším Ollama (llava model)...")
    ollama_result = analyze_with_ollama(image_path)
    if ollama_result:
        results.append(f"Ollama: {ollama_result}")
        print(f"   ✓ Výsledek: {ollama_result}")
    else:
        print("   ✗ Ollama nedostupná")
    
    # Zkusíme Hugging Face
    print("\n2. Zkouším Hugging Face TrOCR...")
    hf_result = analyze_with_huggingface(image_path)
    if hf_result:
        results.append(f"Hugging Face: {hf_result}")
        print(f"   ✓ Výsledek: {hf_result}")
    else:
        print("   ✗ Hugging Face nedostupné")
    
    if results:
        print(f"\n=== VÝSLEDKY AI ANALÝZY ===")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")
        
        print(f"\n=== VÝBĚR NEJLEPŠÍHO VÝSLEDKU ===")
        print(f"Který výsledek vypadá nejlépe? (zadejte číslo 1-{len(results)})")
        print("Nebo zadejte 'm' pro manuální zadání textu")
        print("Nebo zadejte 'q' pro ukončení bez přejmenování")
        
        try:
            choice = input("Vaše volba: ").strip()
            
            if choice.lower() == 'q':
                print("Ukončuji bez přejmenování")
                return
            elif choice.lower() == 'm':
                manual_text = input("Zadejte text z odznaku manuálně: ").strip()
                if manual_text:
                    selected_text = manual_text
                else:
                    print("Prázdný text, ukončuji")
                    return
            else:
                choice_num = int(choice)
                if 1 <= choice_num <= len(results):
                    selected_result = results[choice_num - 1]
                    # Extrakce pouze textu (bez prefixu s metodou)
                    selected_text = selected_result.split(': ', 1)[1] if ': ' in selected_result else selected_result
                else:
                    print("Neplatná volba!")
                    return
            
            print(f"Vybraný text: '{selected_text}'")
            
            # Vyčištění textu pro název souboru
            clean_text = clean_filename(selected_text)
            
            if clean_text:
                # Získání přípony původního souboru
                _, ext = os.path.splitext(image_path)
                new_filename = f"{clean_text}{ext}"
                
                print(f"Navrhovaný nový název: {new_filename}")
                
                # Kontrola, jestli soubor s tímto názvem už neexistuje
                if os.path.exists(new_filename):
                    counter = 1
                    while os.path.exists(f"{clean_text}_{counter}{ext}"):
                        counter += 1
                    new_filename = f"{clean_text}_{counter}{ext}"
                    print(f"Soubor už existuje, použiji: {new_filename}")
                
                # Přejmenování souboru
                try:
                    os.rename(image_path, new_filename)
                    print(f"Soubor úspěšně přejmenován na: {new_filename}")
                except Exception as e:
                    print(f"Chyba při přejmenování: {e}")
            else:
                print("Nepodařilo se vyčistit text pro název souboru")
                
        except ValueError:
            print("Neplatná volba!")
        except KeyboardInterrupt:
            print("\nUkončuji...")
    else:
        print("\n❌ Žádná AI služba není dostupná.")
        print("Možnosti:")
        print("1. Nainstalujte Ollama a stáhněte llava model: 'ollama pull llava'")
        print("2. Nebo zadejte text manuálně:")
        
        manual_text = input("Zadejte text z odznaku: ").strip()
        if manual_text:
            clean_text = clean_filename(manual_text)
            if clean_text:
                _, ext = os.path.splitext(image_path)
                new_filename = f"{clean_text}{ext}"
                
                try:
                    os.rename(image_path, new_filename)
                    print(f"Soubor úspěšně přejmenován na: {new_filename}")
                except Exception as e:
                    print(f"Chyba při přejmenování: {e}")

if __name__ == "__main__":
    main()
