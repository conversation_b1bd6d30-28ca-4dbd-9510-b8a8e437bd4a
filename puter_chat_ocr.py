#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Puter.js Chat OCR automatický renamer
Simuluje puter.ai.chat() API volání
"""

import os
import sys
import re
import requests
import time
import json
import base64

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def encode_image_to_data_url(image_path):
    """
    Převede obrázek na data URL pro Puter API
    """
    try:
        with open(image_path, 'rb') as image_file:
            encoded = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{encoded}"
    except Exception as e:
        print(f"❌ Chyba při kódování obrázku: {e}")
        return None

def extract_text_puter_chat(image_path):
    """
    Extrahuje text pomocí Puter.js chat API
    """
    try:
        print(f"🤖 Nahrávám na Puter.js Chat: {os.path.basename(image_path)}")
        
        # Puter.js chat API endpoint
        url = "https://api.puter.com/drivers/call"
        
        # Převedeme obrázek na data URL
        data_url = encode_image_to_data_url(image_path)
        if not data_url:
            return None
        
        # Parametry pro Puter chat API (simulujeme puter.ai.chat())
        payload = {
            "interface": "puter-chat-completion",
            "driver": "openai",
            "method": "complete",
            "args": {
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "What text do you see on this badge or sign? Extract only the text, nothing else. If it's in Czech, preserve the diacritics (like ř, š, č, ž, etc.)."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": data_url
                                }
                            }
                        ]
                    }
                ]
            }
        }
        
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        # Odeslání požadavku
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            if 'result' in result and result['result']:
                if isinstance(result['result'], dict) and 'choices' in result['result']:
                    # OpenAI formát odpovědi
                    choices = result['result']['choices']
                    if choices and len(choices) > 0:
                        message = choices[0].get('message', {})
                        content = message.get('content', '').strip()
                        if content:
                            print(f"✅ Nalezen text: '{content}'")
                            return content
                elif isinstance(result['result'], str):
                    # Přímý text
                    extracted_text = result['result'].strip()
                    print(f"✅ Nalezen text: '{extracted_text}'")
                    return extracted_text
            
            print("❌ Puter Chat API nevrátilo žádný text")
            return None
        else:
            print(f"❌ Puter Chat API chyba: {response.status_code}")
            if response.text:
                print(f"   Odpověď: {response.text[:300]}")
            return None
                
    except requests.exceptions.Timeout:
        print("❌ Puter Chat API timeout")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ Problém s připojením k Puter Chat API")
        return None
    except Exception as e:
        print(f"❌ Chyba při Puter Chat API: {e}")
        return None

def extract_text_puter_simple_chat(image_path):
    """
    Alternativní jednodušší přístup k Puter chat
    """
    try:
        print(f"🤖 Zkouším jednoduchý Puter chat...")
        
        url = "https://api.puter.com/ai/chat"
        
        data_url = encode_image_to_data_url(image_path)
        if not data_url:
            return None
        
        payload = {
            "message": "What text do you see on this badge? Extract only the text.",
            "image": data_url
        }
        
        response = requests.post(url, json=payload, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            if 'response' in result and result['response'].strip():
                return result['response'].strip()
            elif 'text' in result and result['text'].strip():
                return result['text'].strip()
        
        return None
        
    except Exception as e:
        print(f"❌ Jednoduchý Puter chat selhalo: {e}")
        return None

def process_image(image_path):
    """
    Zpracuje jeden obrázek pomocí Puter Chat OCR
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")
    
    # Zkusíme Puter Chat API
    extracted_text = extract_text_puter_chat(image_path)
    
    # Pokud selhalo, zkusíme jednodušší přístup
    if not extracted_text:
        extracted_text = extract_text_puter_simple_chat(image_path)
    
    if not extracted_text:
        print("❌ Puter Chat OCR nenašlo žádný text - přeskakuji")
        return False
    
    print(f"🎯 Rozpoznaný text: '{extracted_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(extracted_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí Puter Chat OCR
    """
    print("🤖 Puter.js Chat OCR Automatický Renamer pro odznaky")
    print("=" * 60)
    print("💬 Používám Puter.js chat API pro rozpoznání textu")
    print("=" * 60)
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s Puter.js Chat OCR...")
    print("⏱️  Pozor: AI analýza může trvat déle, buďte trpěliví...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1
        
        # Delší pauza mezi požadavky pro AI API
        if i < len(image_files):
            print("⏳ Čekám 5 sekund před dalším obrázkem...")
            time.sleep(5)
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
