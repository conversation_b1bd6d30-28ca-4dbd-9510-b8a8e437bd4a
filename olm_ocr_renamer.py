#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OLM OCR automatický renamer pro odznaky
Používá https://www.olmocr.com/ API
"""

import os
import sys
import re
import requests
import time
import json

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def extract_text_olm_ocr(image_path):
    """
    Extrahuje text pomocí OLM OCR API
    """
    try:
        print(f"🌐 Nahrávám na OLM OCR: {os.path.basename(image_path)}")
        
        # OLM OCR API endpoint
        url = "https://www.olmocr.com/api/v1/ocr"
        
        # Příprava souboru
        with open(image_path, 'rb') as f:
            files = {
                'file': (os.path.basename(image_path), f, 'image/jpeg')
            }
            
            # Parametry pro OCR
            data = {
                'language': 'auto',  # Automatická detekce jazyka
                'output_format': 'text'
            }
            
            # Odeslání požadavku
            response = requests.post(url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if 'text' in result and result['text'].strip():
                    extracted_text = result['text'].strip()
                    print(f"✅ Nalezen text: '{extracted_text}'")
                    return extracted_text
                else:
                    print("❌ OLM OCR nevrátilo žádný text")
                    return None
            else:
                print(f"❌ OLM OCR chyba: {response.status_code}")
                if response.text:
                    print(f"   Odpověď: {response.text[:200]}")
                return None
                
    except requests.exceptions.Timeout:
        print("❌ OLM OCR timeout - zkusím znovu za chvíli")
        time.sleep(2)
        return None
    except requests.exceptions.ConnectionError:
        print("❌ Problém s připojením k OLM OCR")
        return None
    except Exception as e:
        print(f"❌ Chyba při OLM OCR: {e}")
        return None

def extract_text_olm_ocr_alternative(image_path):
    """
    Alternativní přístup k OLM OCR (pokud má jiné API)
    """
    try:
        print(f"🌐 Zkouším alternativní OLM OCR...")
        
        # Možná má OLM OCR jiný endpoint
        url = "https://api.olmocr.com/v1/extract"
        
        with open(image_path, 'rb') as f:
            files = {'image': f}
            
            response = requests.post(url, files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'extracted_text' in result:
                    return result['extracted_text'].strip()
            
        return None
        
    except Exception as e:
        print(f"❌ Alternativní OLM OCR selhalo: {e}")
        return None

def process_image(image_path):
    """
    Zpracuje jeden obrázek pomocí OLM OCR
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")
    
    # Zkusíme hlavní OLM OCR API
    extracted_text = extract_text_olm_ocr(image_path)
    
    # Pokud selhalo, zkusíme alternativní přístup
    if not extracted_text:
        extracted_text = extract_text_olm_ocr_alternative(image_path)
    
    if not extracted_text:
        print("❌ OLM OCR nenašlo žádný text - přeskakuji")
        return False
    
    print(f"🎯 Rozpoznaný text: '{extracted_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(extracted_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí OLM OCR
    """
    print("🌐 OLM OCR Automatický Renamer pro odznaky")
    print("=" * 60)
    print("🤖 Používám https://www.olmocr.com/ pro rozpoznání textu")
    print("=" * 60)
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s OLM OCR...")
    print("⏱️  Pozor: Online OCR může být pomalejší, buďte trpěliví...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1
        
        # Krátká pauza mezi požadavky, aby nedošlo k přetížení API
        if i < len(image_files):
            print("⏳ Čekám 2 sekundy před dalším obrázkem...")
            time.sleep(2)
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
