#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EasyOCR automatický renamer pro odznaky
Používá EasyOCR pro přesné rozpoznání textu
"""

import os
import sys
import re
import easyocr
import cv2
import numpy as np
from PIL import Image, ImageEnhance

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def preprocess_image(image_path):
    """
    Předzpracuje obrázek pro lepší OCR
    """
    try:
        # Načtení obrázku
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        # Konverze do odstínů šedi
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Zvětšení obrázku pro lepší rozpoznání
        height, width = gray.shape
        scale_factor = max(2, 800 // max(height, width))
        new_width = width * scale_factor
        new_height = height * scale_factor
        resized = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Vylepšení kontrastu
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(resized)
        
        # Gaussovo rozmazání pro odstranění šumu
        blurred = cv2.GaussianBlur(enhanced, (1, 1), 0)
        
        # Prahování pro lepší kontrast
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return thresh
        
    except Exception as e:
        print(f"❌ Chyba při předzpracování obrázku: {e}")
        return None

def extract_text_easyocr(image_path):
    """
    Extrahuje text pomocí EasyOCR s různými nastaveními
    """
    try:
        print(f"🔍 Analyzuji: {os.path.basename(image_path)}")
        
        # Inicializace EasyOCR s češtinou a angličtinou
        reader = easyocr.Reader(['cs', 'en'], gpu=False)
        
        results = []
        
        # Metoda 1: Původní obrázek
        try:
            result1 = reader.readtext(image_path, detail=0, paragraph=False)
            if result1:
                combined_text = ' '.join(result1).strip()
                if combined_text:
                    results.append(f"Původní: {combined_text}")
        except Exception as e:
            print(f"   ⚠️ Metoda 1 selhala: {e}")
        
        # Metoda 2: Předzpracovaný obrázek
        try:
            processed_img = preprocess_image(image_path)
            if processed_img is not None:
                result2 = reader.readtext(processed_img, detail=0, paragraph=False)
                if result2:
                    combined_text = ' '.join(result2).strip()
                    if combined_text:
                        results.append(f"Předzpracovaný: {combined_text}")
        except Exception as e:
            print(f"   ⚠️ Metoda 2 selhala: {e}")
        
        # Metoda 3: S detailními výsledky pro filtrování podle confidence
        try:
            detailed_results = reader.readtext(image_path, detail=1, paragraph=False)
            high_conf_texts = []
            for (bbox, text, confidence) in detailed_results:
                if confidence > 0.5:  # Pouze texty s vysokou spolehlivostí
                    high_conf_texts.append(text)
            
            if high_conf_texts:
                combined_text = ' '.join(high_conf_texts).strip()
                if combined_text:
                    results.append(f"Vysoká spolehlivost: {combined_text}")
        except Exception as e:
            print(f"   ⚠️ Metoda 3 selhala: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Chyba při EasyOCR: {e}")
        return []

def process_image(image_path):
    """
    Zpracuje jeden obrázek pomocí EasyOCR
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")
    
    # Extrakce textu pomocí EasyOCR
    results = extract_text_easyocr(image_path)
    
    if not results:
        print("❌ EasyOCR nenašlo žádný text - přeskakuji")
        return False
    
    print(f"📋 Nalezené texty:")
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result}")
    
    # Automaticky vybereme první (nejlepší) výsledek
    selected_result = results[0]
    
    # Extrakce čistého textu
    if ': ' in selected_result:
        selected_text = selected_result.split(': ', 1)[1]
    else:
        selected_text = selected_result
    
    print(f"🎯 Vybrán text: '{selected_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí EasyOCR
    """
    print("🤖 EasyOCR Automatický Renamer pro odznaky")
    print("=" * 60)
    print("🎯 Používám EasyOCR pro přesné rozpoznání textu")
    print("=" * 60)
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s EasyOCR...")
    print("⏱️  První spuštění může trvat déle kvůli stahování modelů...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
