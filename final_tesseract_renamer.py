#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Finální Tesseract OCR renamer s optimalizovaným nastavením
"""

import os
import sys
import re
from PIL import Image, ImageEnhance, ImageFilter
import pytesseract

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def enhance_image_for_ocr(image_path):
    """
    Vylepší obrázek pro lepší OCR
    """
    try:
        image = Image.open(image_path)
        
        # Konverze do odstínů šedi
        if image.mode != 'L':
            image = image.convert('L')
        
        # Zvětšení obrázku
        width, height = image.size
        scale_factor = max(3, 1200 // max(width, height))
        new_size = (width * scale_factor, height * scale_factor)
        image = image.resize(new_size, Image.LANCZOS)
        
        # Zvýšení kontrastu
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(2.5)
        
        # Zvýšení ostrosti
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(2.0)
        
        # Filtr pro odstranění šumu
        image = image.filter(ImageFilter.MedianFilter(size=3))
        
        return image
        
    except Exception as e:
        print(f"❌ Chyba při vylepšení obrázku: {e}")
        return None

def extract_text_tesseract(image_path):
    """
    Extrahuje text pomocí Tesseract s různými nastaveními
    """
    try:
        results = []
        
        # Metoda 1: Původní obrázek s různými PSM
        original_image = Image.open(image_path)
        
        psm_modes = [8, 7, 6, 13]  # Různé page segmentation modes
        
        for psm in psm_modes:
            try:
                config = f'--psm {psm} -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽabcdefghijklmnopqrstuvwxyzáčďéěíňóřšťúůýž0123456789 '
                text = pytesseract.image_to_string(original_image, lang='ces+eng', config=config)
                if text.strip():
                    results.append(f"PSM{psm}: {text.strip()}")
            except:
                pass
        
        # Metoda 2: Vylepšený obrázek
        enhanced = enhance_image_for_ocr(image_path)
        if enhanced:
            for psm in [8, 7]:
                try:
                    config = f'--psm {psm} -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽabcdefghijklmnopqrstuvwxyzáčďéěíňóřšťúůýž0123456789 '
                    text = pytesseract.image_to_string(enhanced, lang='ces+eng', config=config)
                    if text.strip():
                        results.append(f"Enhanced PSM{psm}: {text.strip()}")
                except:
                    pass
        
        # Metoda 3: Pouze velká písmena
        try:
            config = '--psm 8 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ0123456789 '
            text = pytesseract.image_to_string(enhanced or original_image, lang='ces+eng', config=config)
            if text.strip():
                results.append(f"Uppercase: {text.strip()}")
        except:
            pass
        
        # Metoda 4: Bez whitelistu
        try:
            text = pytesseract.image_to_string(enhanced or original_image, lang='ces+eng', config='--psm 8')
            if text.strip():
                results.append(f"No whitelist: {text.strip()}")
        except:
            pass
        
        return results
        
    except Exception as e:
        print(f"❌ Chyba při Tesseract OCR: {e}")
        return []

def process_image(image_path):
    """
    Zpracuje jeden obrázek
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")
    
    # Extrakce textu pomocí Tesseract
    results = extract_text_tesseract(image_path)
    
    if not results:
        print("❌ Tesseract nenašel žádný text - přeskakuji")
        return False
    
    print(f"📋 Nalezené texty:")
    for i, result in enumerate(results, 1):
        print(f"  {i}. {result}")
    
    # Automaticky vybereme první (nejlepší) výsledek
    selected_result = results[0]
    
    # Extrakce čistého textu
    if ': ' in selected_result:
        selected_text = selected_result.split(': ', 1)[1]
    else:
        selected_text = selected_result
    
    print(f"🎯 Vybrán text: '{selected_text}'")
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování pomocí optimalizovaného Tesseract
    """
    print("🤖 Finální Tesseract OCR Renamer pro odznaky")
    print("=" * 60)
    print("🎯 Používám optimalizovaný Tesseract s více metodami")
    print("=" * 60)
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme pouze soubor Příbram (už správně přejmenovaný)
            if not file.startswith('Příbram'):
                image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        
        # Zobrazíme všechny soubory pro debug
        all_files = [f for f in os.listdir('.') if any(f.lower().endswith(ext) for ext in image_extensions)]
        if all_files:
            print("📁 Nalezené obrázky:")
            for f in all_files:
                print(f"  - {f}")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám automatické zpracování s optimalizovaným Tesseract...")
    
    processed = 0
    skipped = 0
    
    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
