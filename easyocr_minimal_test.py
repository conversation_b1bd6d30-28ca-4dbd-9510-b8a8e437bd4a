#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimální test EasyOCR podle GitHub dokumentace
"""

import os
import easyocr

def minimal_test():
    """
    Minimální test podle EasyOCR dokumentace
    """
    try:
        print("🔍 Minimální test EasyOCR...")
        
        # Načtení OCR čtečky podle dokumentace
        reader = easyocr.Reader(['cs','en'], gpu=False)
        print("✅ EasyOCR Reader vytvořen")
        
        # Najdeme první obrázek
        image_files = []
        for file in os.listdir('.'):
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_files.append(file)
        
        if not image_files:
            print("❌ Žádné obrázky nenalezeny")
            return
        
        test_image = image_files[0]
        print(f"🔍 Testuji na: {test_image}")
        
        # Základní OCR test podle dokumentace
        print("🔍 Spouštím readtext...")
        result = reader.readtext(test_image)
        
        print(f"✅ OCR dokončeno! Nalezeno {len(result)} výsledků:")
        for detection in result:
            print(f"  - {detection}")
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    minimal_test()
