#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR skript pro čtení textu z odznaků na obrázcích
"""

import os
import sys
import re
from paddleocr import PaddleOCR

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def extract_text_with_paddle(image_path):
    """
    Extrahuje text z obrázku pomocí PaddleOCR
    """
    try:
        # Použijeme globální instanci OCR
        global ocr_instance

        # Spuštění OCR - klasické API
        result = ocr_instance.ocr(image_path)

        if not result or not result[0]:
            return []

        # Extrakce textu z výsledků
        texts = []
        all_text_parts = []

        for line in result[0]:
            if len(line) >= 2:
                text = line[1][0].strip()  # Text je v line[1][0]
                confidence = line[1][1]  # Confidence je v line[1][1]

                if confidence > 0.3:  # Snížená hranice pro lepší zachycení
                    texts.append(f"{text} (conf: {confidence:.2f})")
                    all_text_parts.append(text)

        # Pokusíme se spojit všechny části do jednoho textu
        if all_text_parts:
            combined_text = ' '.join(all_text_parts)
            texts.insert(0, f"KOMBINOVANÝ: {combined_text}")

        return texts

    except Exception as e:
        print(f"❌ Chyba při PaddleOCR: {e}")
        return []

def process_image(image_path):
    """
    Zpracuje jeden obrázek - automaticky vybere nejlepší výsledek
    """
    if not os.path.exists(image_path):
        print(f"❌ Soubor {image_path} neexistuje!")
        return False

    print(f"\n{'='*50}")
    print(f"📁 {os.path.basename(image_path)}")
    print(f"{'='*50}")

    # Extrakce textu pomocí PaddleOCR
    paddle_results = extract_text_with_paddle(image_path)

    if not paddle_results:
        print("❌ PaddleOCR nenašlo žádný text - přeskakuji")
        return False

    print(f"\n📋 Nalezené texty:")
    for i, result in enumerate(paddle_results, 1):
        print(f"  {i}. {result}")

    # Automaticky vybereme první (nejlepší) výsledek
    selected_result = paddle_results[0]

    # Extrakce čistého textu
    if 'KOMBINOVANÝ:' in selected_result:
        # Pro kombinovaný text
        selected_text = selected_result.split('KOMBINOVANÝ: ')[1]
    else:
        # Pro jednotlivé výsledky
        if ' (conf:' in selected_result:
            selected_text = selected_result.split(' (conf:')[0]
        else:
            selected_text = selected_result

    print(f"🎯 Vybrán text: '{selected_text}'")

    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)

    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru - přeskakuji")
        return False
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce - automatické přejmenování všech obrázků
    """
    print("🔍 PaddleOCR Automatický Renamer pro odznaky")
    print("=" * 60)
    print("🤖 Automaticky přejmenuji všechny obrázky podle rozpoznaného textu")
    print("=" * 60)

    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []

    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Přeskočíme soubory, které už vypadají jako přejmenované
            if not any(char.isdigit() for char in file[:8]):  # Pokud název nezačíná čísly
                image_files.append(file)

    if not image_files:
        print("❌ Nenašel jsem žádné obrázky k přejmenování!")
        print("💡 Hledám soubory s příponami: " + ", ".join(image_extensions))
        return

    print(f"📁 Nalezeno {len(image_files)} obrázků k zpracování:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")

    print(f"\n🚀 Začínám automatické zpracování...")

    processed = 0
    skipped = 0

    # Inicializace PaddleOCR jednou pro všechny obrázky
    print("⚙️  Inicializuji PaddleOCR...")
    global ocr_instance
    try:
        from paddleocr import PaddleOCR
        ocr_instance = PaddleOCR(lang='en')
        print("✅ PaddleOCR připraven")
    except Exception as e:
        print(f"❌ Chyba při inicializaci PaddleOCR: {e}")
        return

    for i, image_file in enumerate(image_files, 1):
        print(f"\n📷 [{i}/{len(image_files)}]", end=" ")
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1

    print(f"\n" + "="*60)
    print(f"📊 FINÁLNÍ SHRNUTÍ:")
    print(f"✅ Úspěšně přejmenováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem zpracováno: {len(image_files)}")
    print(f"🎯 Úspěšnost: {(processed/len(image_files)*100):.1f}%" if image_files else "0%")
    print("="*60)

if __name__ == "__main__":
    main()
