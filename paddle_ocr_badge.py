#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR skript pro čtení textu z odznaků na obrázcích
"""

import os
import sys
import re
from paddleocr import PaddleOCR

def clean_filename(text):
    """
    Vyčistí text pro použití jako název souboru
    """
    if not text:
        return ""
    
    # Odstranění speciálních znaků
    cleaned = re.sub(r'[<>:"/\\|?*]', '', text)
    # Nahrazení více mezer jednou
    cleaned = re.sub(r'\s+', '_', cleaned)
    # Odstranění podtržítek na začátku a konci
    cleaned = cleaned.strip('_')
    # Omezení délky
    if len(cleaned) > 50:
        cleaned = cleaned[:50]
    
    return cleaned

def extract_text_with_paddle(image_path):
    """
    Extrahuje text z obrázku pomocí PaddleOCR
    """
    try:
        print(f"Inicializuji PaddleOCR...")
        # Inicializace PaddleOCR s podporou češtiny a angličtiny
        ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        
        print(f"Analyzuji obrázek: {image_path}")
        # Spuštění OCR
        result = ocr.ocr(image_path, cls=True)
        
        if not result or not result[0]:
            return []
        
        # Extrakce textu z výsledků
        texts = []
        for line in result[0]:
            if len(line) >= 2:
                text = line[1][0]  # Text je v line[1][0]
                confidence = line[1][1]  # Confidence je v line[1][1]
                if confidence > 0.5:  # Pouze texty s vysokou spolehlivostí
                    texts.append(f"{text} (confidence: {confidence:.2f})")
        
        return texts
        
    except Exception as e:
        print(f"Chyba při PaddleOCR: {e}")
        return []

def process_image(image_path):
    """
    Zpracuje jeden obrázek
    """
    if not os.path.exists(image_path):
        print(f"Soubor {image_path} neexistuje!")
        return False
    
    print(f"\n{'='*60}")
    print(f"Zpracovávám: {image_path}")
    print(f"{'='*60}")
    
    # Extrakce textu pomocí PaddleOCR
    paddle_results = extract_text_with_paddle(image_path)
    
    all_results = []
    
    if paddle_results:
        print(f"\n📋 PaddleOCR výsledky:")
        for i, result in enumerate(paddle_results, 1):
            print(f"  {i}. {result}")
            all_results.append(f"PaddleOCR {i}: {result}")
    else:
        print("❌ PaddleOCR nenašlo žádný text")
    
    if not all_results:
        print("❌ Žádná metoda nevrátila výsledek")
        manual_text = input("Zadejte text z odznaku manuálně: ").strip()
        if manual_text:
            selected_text = manual_text
        else:
            print("Přeskakuji soubor...")
            return False
    else:
        print(f"\n🎯 Výběr nejlepšího výsledku:")
        for i, result in enumerate(all_results, 1):
            print(f"  {i}. {result}")
        
        print(f"\nKterý výsledek vypadá nejlépe? (1-{len(all_results)})")
        print("Nebo 'm' pro manuální zadání, 's' pro přeskočení")
        
        choice = input("Vaše volba: ").strip()
        
        if choice.lower() == 's':
            print("Přeskakuji soubor...")
            return False
        elif choice.lower() == 'm':
            manual_text = input("Zadejte správný text z odznaku: ").strip()
            if manual_text:
                selected_text = manual_text
            else:
                print("Prázdný text, přeskakuji...")
                return False
        else:
            try:
                choice_num = int(choice)
                if 1 <= choice_num <= len(all_results):
                    selected_result = all_results[choice_num - 1]
                    # Extrakce pouze textu (bez prefixu s metodou a confidence)
                    if ': ' in selected_result:
                        text_part = selected_result.split(': ', 1)[1]
                        # Odstranění confidence informace
                        if ' (confidence:' in text_part:
                            text_part = text_part.split(' (confidence:')[0]
                        selected_text = text_part
                    else:
                        selected_text = selected_result
                else:
                    print("Neplatná volba!")
                    return False
            except ValueError:
                print("Neplatná volba!")
                return False
    
    # Vyčištění textu pro název souboru
    clean_text = clean_filename(selected_text)
    
    if not clean_text:
        print("❌ Nepodařilo se vyčistit text pro název souboru")
        return False
    
    # Získání přípony původního souboru
    _, ext = os.path.splitext(image_path)
    new_filename = f"{clean_text}{ext}"
    
    # Kontrola, jestli soubor s tímto názvem už neexistuje
    if os.path.exists(new_filename):
        counter = 1
        while os.path.exists(f"{clean_text}_{counter}{ext}"):
            counter += 1
        new_filename = f"{clean_text}_{counter}{ext}"
        print(f"⚠️  Soubor už existuje, použiji: {new_filename}")
    
    # Přejmenování souboru
    try:
        os.rename(image_path, new_filename)
        print(f"✅ Soubor úspěšně přejmenován na: {new_filename}")
        return True
    except Exception as e:
        print(f"❌ Chyba při přejmenování: {e}")
        return False

def main():
    """
    Hlavní funkce
    """
    print("🔍 PaddleOCR Batch Renamer pro odznaky")
    print("=" * 50)
    
    # Najdeme všechny obrázky v aktuální složce
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    if not image_files:
        print("❌ Nenašel jsem žádné obrázky v aktuální složce!")
        return
    
    print(f"📁 Nalezeno {len(image_files)} obrázků:")
    for i, file in enumerate(image_files, 1):
        print(f"  {i}. {file}")
    
    print(f"\n🚀 Začínám zpracování...")
    
    processed = 0
    skipped = 0
    
    for image_file in image_files:
        success = process_image(image_file)
        if success:
            processed += 1
        else:
            skipped += 1
    
    print(f"\n📊 SHRNUTÍ:")
    print(f"✅ Úspěšně zpracováno: {processed}")
    print(f"⏭️  Přeskočeno: {skipped}")
    print(f"📁 Celkem souborů: {len(image_files)}")

if __name__ == "__main__":
    main()
