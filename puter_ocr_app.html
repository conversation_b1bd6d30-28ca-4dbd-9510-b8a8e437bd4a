<!DOCTYPE html>
<html>
<head>
    <title>Puter.js OCR Renamer pro odznaky</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input[type="file"] { margin: 10px 0; }
        .rename-suggestion { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Puter.js OCR Renamer pro odznaky</h1>
        <p><PERSON><PERSON><PERSON> r<PERSON>znání textu na odznacích pomocí Puter.js AI</p>
        
        <div id="auth-status" class="result info">
            <p>🔐 Připojuji se k Puter.js...</p>
        </div>
        
        <div id="test-section" style="display: none;">
            <h2>Test s demo obrázkem</h2>
            <button onclick="testDemo()">🧪 Otestovat Puter.js</button>
            <div id="demo-result"></div>
        </div>
        
        <div id="upload-section" style="display: none;">
            <h2>Analýza vašich odznaků</h2>
            <input type="file" id="fileInput" accept="image/*" multiple>
            <button onclick="analyzeImages()">🔍 Analyzovat obrázky</button>
            <div id="results"></div>
        </div>
        
        <div id="rename-section" style="display: none;">
            <h2>Návrhy přejmenování</h2>
            <div id="rename-suggestions"></div>
            <button onclick="downloadRenameScript()" style="display: none;" id="downloadBtn">
                💾 Stáhnout skript pro přejmenování
            </button>
        </div>
    </div>
    
    <script src="https://js.puter.com/v2/"></script>
    
    <script>
        let analysisResults = [];
        
        async function initPuter() {
            const authStatus = document.getElementById('auth-status');
            
            try {
                authStatus.innerHTML = '<p>🔐 Ověřuji přístup k Puter.js AI...</p>';
                
                // Zkusíme jednoduchý test, který vyvolá autentifikaci
                await puter.ai.chat('Hello', 'https://assets.puter.site/doge.jpeg');
                
                authStatus.innerHTML = '<p class="success">✅ Puter.js je připraven! Můžete pokračovat.</p>';
                document.getElementById('test-section').style.display = 'block';
                document.getElementById('upload-section').style.display = 'block';
                
            } catch (error) {
                authStatus.innerHTML = '<p class="error">❌ Chyba při připojení: ' + error + '</p>';
                authStatus.innerHTML += '<p>💡 Zkuste se přihlásit k Puter.com účtu a obnovit stránku.</p>';
            }
        }
        
        async function testDemo() {
            const demoResult = document.getElementById('demo-result');
            
            try {
                demoResult.innerHTML = '<p>🤖 Testuji s demo obrázkem...</p>';
                
                const result = await puter.ai.chat(
                    'What do you see in this image? Describe briefly.', 
                    'https://assets.puter.site/doge.jpeg'
                );
                
                demoResult.innerHTML = '<div class="result success"><strong>✅ Test úspěšný!</strong><br>Výsledek: ' + result + '</div>';
                
            } catch (error) {
                demoResult.innerHTML = '<div class="result error">❌ Test selhal: ' + error + '</div>';
            }
        }
        
        async function analyzeImages() {
            const fileInput = document.getElementById('fileInput');
            const resultsDiv = document.getElementById('results');
            const files = fileInput.files;
            
            if (files.length === 0) {
                alert('Vyberte prosím alespoň jeden obrázek!');
                return;
            }
            
            resultsDiv.innerHTML = '<p>🔍 Analyzuji ' + files.length + ' obrázků...</p>';
            analysisResults = [];
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                try {
                    resultsDiv.innerHTML += '<p>📷 [' + (i+1) + '/' + files.length + '] Analyzuji: ' + file.name + '</p>';
                    
                    const dataUrl = await fileToDataUrl(file);
                    
                    const ocrResult = await puter.ai.chat(
                        'What text do you see on this badge or sign? Extract only the text, preserve Czech diacritics (ř, š, č, ž, etc.). Return only the text, nothing else.',
                        dataUrl
                    );
                    
                    const cleanText = ocrResult.trim();
                    
                    analysisResults.push({
                        originalName: file.name,
                        detectedText: cleanText,
                        suggestedName: cleanFilename(cleanText) + getFileExtension(file.name)
                    });
                    
                    resultsDiv.innerHTML += '<div class="result success">✅ <strong>' + file.name + ':</strong> "' + cleanText + '"</div>';
                    
                } catch (error) {
                    resultsDiv.innerHTML += '<div class="result error">❌ Chyba při analýze ' + file.name + ': ' + error + '</div>';
                    
                    analysisResults.push({
                        originalName: file.name,
                        detectedText: 'CHYBA',
                        suggestedName: file.name
                    });
                }
                
                // Krátká pauza mezi požadavky
                if (i < files.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }
            
            showRenameSuggestions();
        }
        
        function fileToDataUrl(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }
        
        function cleanFilename(text) {
            if (!text || text === 'CHYBA') return 'unknown';
            
            // Odstranění speciálních znaků
            let cleaned = text.replace(/[<>:"/\\|?*]/g, '');
            // Nahrazení více mezer jednou
            cleaned = cleaned.replace(/\s+/g, '_');
            // Odstranění podtržítek na začátku a konci
            cleaned = cleaned.replace(/^_+|_+$/g, '');
            // Omezení délky
            if (cleaned.length > 50) {
                cleaned = cleaned.substring(0, 50);
            }
            
            return cleaned || 'unknown';
        }
        
        function getFileExtension(filename) {
            return filename.substring(filename.lastIndexOf('.'));
        }
        
        function showRenameSuggestions() {
            const suggestionsDiv = document.getElementById('rename-suggestions');
            const renameSection = document.getElementById('rename-section');
            const downloadBtn = document.getElementById('downloadBtn');
            
            let html = '<h3>📋 Návrhy přejmenování:</h3>';
            
            analysisResults.forEach((result, index) => {
                html += '<div class="rename-suggestion">';
                html += '<strong>📁 ' + result.originalName + '</strong><br>';
                html += '🎯 Rozpoznaný text: "' + result.detectedText + '"<br>';
                html += '📝 Navrhovaný název: <strong>' + result.suggestedName + '</strong>';
                html += '</div>';
            });
            
            suggestionsDiv.innerHTML = html;
            renameSection.style.display = 'block';
            downloadBtn.style.display = 'block';
        }
        
        function downloadRenameScript() {
            let script = '#!/bin/bash\n# Skript pro přejmenování souborů podle OCR výsledků\n\n';
            
            analysisResults.forEach(result => {
                if (result.detectedText !== 'CHYBA') {
                    script += 'mv "' + result.originalName + '" "' + result.suggestedName + '"\n';
                    script += 'echo "✅ Přejmenováno: ' + result.originalName + ' → ' + result.suggestedName + '"\n';
                } else {
                    script += '# CHYBA: ' + result.originalName + ' - nepodařilo se rozpoznat text\n';
                }
            });
            
            script += '\necho "🎯 Přejmenování dokončeno!"';
            
            const blob = new Blob([script], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'rename_badges.sh';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // Spustíme inicializaci po načtení stránky
        window.onload = initPuter;
    </script>
</body>
</html>
