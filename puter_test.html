<!DOCTYPE html>
<html>
<head>
    <title>Puter.js OCR Test</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Puter.js OCR Test pro odznaky</h1>
    
    <div id="results"></div>
    
    <script src="https://js.puter.com/v2/"></script>
    
    <script>
        async function testPuterOCR() {
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML += '<p>🤖 Testuji Puter.js OCR...</p>';
                
                // Test s demo obrázkem
                const result = await puter.ai.chat(
                    'What text do you see in this image? Extract only the text, nothing else.', 
                    'https://assets.puter.site/doge.jpeg'
                );
                
                resultsDiv.innerHTML += '<p>✅ Puter.js funguje!</p>';
                resultsDiv.innerHTML += '<p><strong>Výsledek:</strong> ' + result + '</p>';
                
                // <PERSON><PERSON><PERSON> z<PERSON> s lokálním souborem (pokud je dostupný)
                resultsDiv.innerHTML += '<p>📁 Zkouším lokální soubory...</p>';
                
                // Vytvoříme file input pro testování
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.accept = 'image/*';
                fileInput.onchange = async function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = async function(event) {
                            const dataUrl = event.target.result;
                            
                            try {
                                resultsDiv.innerHTML += '<p>🔍 Analyzuji: ' + file.name + '</p>';
                                
                                const ocrResult = await puter.ai.chat(
                                    'What text do you see on this badge or sign? Extract only the text, nothing else.',
                                    dataUrl
                                );
                                
                                resultsDiv.innerHTML += '<p>✅ <strong>' + file.name + ':</strong> ' + ocrResult + '</p>';
                                
                            } catch (error) {
                                resultsDiv.innerHTML += '<p>❌ Chyba při analýze ' + file.name + ': ' + error + '</p>';
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                };
                
                resultsDiv.innerHTML += '<p>📤 Vyberte obrázek odznaku:</p>';
                resultsDiv.appendChild(fileInput);
                
            } catch (error) {
                resultsDiv.innerHTML += '<p>❌ Chyba: ' + error + '</p>';
            }
        }
        
        // Spustíme test po načtení stránky
        window.onload = testPuterOCR;
    </script>
</body>
</html>
